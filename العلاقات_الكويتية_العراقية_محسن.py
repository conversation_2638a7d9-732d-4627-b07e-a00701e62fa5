#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف إكسل محسن للعلاقات الكويتية العراقية عبر التاريخ
"""

from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side, NamedStyle
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
import datetime

def create_enhanced_kuwait_iraq_relations_excel():
    """إنشاء ملف إكسل محسن للعلاقات الكويتية العراقية"""

    # البيانات التاريخية المحسنة والمفصلة
    historical_data = [
        {
            'الفترة الزمنية': 'العصر التأسيسي\n(1600-1750م)',
            'السنة': '1613م',
            'الحدث/التطور': 'تأسيس مدينة الكويت وبداية العلاقات التجارية مع العراق',
            'طبيعة العلاقة': 'تجارية ودية',
            'التفاصيل': 'تأسيس مدينة الكويت على يد آل صباح وبداية التجارة البحرية النشطة مع موانئ البصرة والفاو العراقية. ازدهار تجارة اللؤلؤ والتوابل والأقمشة.',
            'التأثير': 'إيجابي قوي',
            'الأهمية': 'عالية جداً',
            'ملاحظات': 'أساس العلاقات التاريخية'
        },
        {
            'الفترة الزمنية': 'العهد العثماني المتقدم\n(1750-1871م)',
            'السنة': '1750-1871م',
            'الحدث/التطور': 'ازدهار العلاقات التجارية في ظل الإدارة العثمانية',
            'طبيعة العلاقة': 'تجارية مستقرة',
            'التفاصيل': 'تطور شبكة تجارية واسعة بين الكويت والعراق. الكويت تصبح مركزاً تجارياً مهماً لنقل البضائع من وإلى العراق عبر الخليج العربي.',
            'التأثير': 'إيجابي',
            'الأهمية': 'عالية',
            'ملاحظات': 'فترة استقرار اقتصادي'
        },
        {
            'الفترة الزمنية': 'العهد العثماني المتأخر\n(1871-1918م)',
            'السنة': '1871-1918م',
            'الحدث/التطور': 'الكويت تحت الحماية البريطانية مع استمرار العلاقات مع العراق',
            'طبيعة العلاقة': 'تجارية محدودة',
            'التفاصيل': 'رغم الحماية البريطانية للكويت عام 1899، استمرت العلاقات التجارية والثقافية مع العراق. تأثر متبادل في الثقافة والتجارة.',
            'التأثير': 'إيجابي محدود',
            'الأهمية': 'متوسطة',
            'ملاحظات': 'بداية التأثير البريطاني'
        },
        {
            'الفترة الزمنية': 'فترة الانتداب البريطاني\n(1918-1932م)',
            'السنة': '1918م',
            'الحدث/التطور': 'الفصل الإداري الرسمي بين الكويت والعراق',
            'طبيعة العلاقة': 'انفصال إداري',
            'التفاصيل': 'بريطانيا تفصل الكويت إدارياً عن ولاية البصرة العراقية وتجعلها محمية بريطانية منفصلة. إنشاء حدود سياسية واضحة.',
            'التأثير': 'محايد',
            'الأهمية': 'عالية جداً',
            'ملاحظات': 'نقطة تحول تاريخية'
        },
        {
            'الفترة الزمنية': 'عهد الاستقلال العراقي\n(1932-1961م)',
            'السنة': '1932-1961م',
            'الحدث/التطور': 'استقلال العراق وتطوير العلاقات الدبلوماسية مع الكويت',
            'طبيعة العلاقة': 'دبلوماسية طبيعية',
            'التفاصيل': 'بعد استقلال العراق عام 1932، تطورت علاقات دبلوماسية طبيعية مع الكويت. تبادل تجاري وثقافي منتظم.',
            'التأثير': 'إيجابي',
            'الأهمية': 'متوسطة',
            'ملاحظات': 'فترة استقرار نسبي'
        },
        {
            'الفترة الزمنية': 'استقلال الكويت\n(1961م)',
            'السنة': '19 يونيو 1961م',
            'الحدث/التطور': 'استقلال الكويت والاعتراف العراقي الرسمي',
            'طبيعة العلاقة': 'دبلوماسية رسمية',
            'التفاصيل': 'استقلال دولة الكويت وإنهاء الحماية البريطانية. العراق يعترف رسمياً بالكويت كدولة مستقلة ذات سيادة.',
            'التأثير': 'إيجابي قوي',
            'الأهمية': 'عالية جداً',
            'ملاحظات': 'بداية العلاقات الرسمية'
        },
        {
            'الفترة الزمنية': 'عهد التعاون الإقليمي\n(1961-1980م)',
            'السنة': '1961-1980م',
            'الحدث/التطور': 'تطوير العلاقات الاقتصادية والسياسية',
            'طبيعة العلاقة': 'تعاون متبادل',
            'التفاصيل': 'تطوير علاقات اقتصادية قوية، تبادل تجاري متزايد، تعاون في المنظمات العربية والإسلامية. الكويت تدعم العراق في قضايا إقليمية.',
            'التأثير': 'إيجابي',
            'الأهمية': 'عالية',
            'ملاحظات': 'ذروة التعاون الإقليمي'
        },
        {
            'الفترة الزمنية': 'الحرب العراقية الإيرانية\n(1980-1988م)',
            'السنة': '22 سبتمبر 1980م',
            'الحدث/التطور': 'الدعم الكويتي الكامل للعراق في حربه ضد إيران',
            'طبيعة العلاقة': 'تحالف استراتيجي',
            'التفاصيل': 'الكويت تقدم دعماً مالياً ولوجستياً ضخماً للعراق يقدر بمليارات الدولارات. فتح الموانئ الكويتية للتجارة العراقية.',
            'التأثير': 'إيجابي استثنائي',
            'الأهمية': 'عالية جداً',
            'ملاحظات': 'أقوى فترات التحالف'
        },
        {
            'الفترة الزمنية': 'فترة التوتر المتصاعد\n(1988-1990م)',
            'السنة': '1988-1990م',
            'الحدث/التطور': 'تدهور العلاقات بسبب الخلافات الاقتصادية والحدودية',
            'طبيعة العلاقة': 'متوترة ومتدهورة',
            'التفاصيل': 'خلافات حول سداد ديون الحرب، نزاعات حدودية حول حقول النفط، اتهامات عراقية للكويت بالإفراط في إنتاج النفط.',
            'التأثير': 'سلبي متزايد',
            'الأهمية': 'عالية',
            'ملاحظات': 'مقدمات الأزمة الكبرى'
        },
        {
            'الفترة الزمنية': 'الغزو والاحتلال\n(1990-1991م)',
            'السنة': '2 أغسطس 1990م',
            'الحدث/التطور': 'الغزو العراقي الشامل للكويت واحتلالها',
            'طبيعة العلاقة': 'عدائية - احتلال عسكري',
            'التفاصيل': 'القوات العراقية تغزو وتحتل الكويت بالكامل. تدمير البنية التحتية، نهب الممتلكات، انتهاكات حقوق الإنسان.',
            'التأثير': 'كارثي ومدمر',
            'الأهمية': 'عالية جداً',
            'ملاحظات': 'أسوأ فترة في التاريخ'
        },
        {
            'الفترة الزمنية': 'التحرير والانتصار\n(1991م)',
            'السنة': '26 فبراير 1991م',
            'الحدث/التطور': 'تحرير الكويت من الاحتلال العراقي',
            'طبيعة العلاقة': 'قطع كامل للعلاقات',
            'التفاصيل': 'تحرير الكويت بواسطة التحالف الدولي بقيادة الولايات المتحدة. انسحاب القوات العراقية وإحراق آبار النفط.',
            'التأثير': 'إيجابي للكويت',
            'الأهمية': 'عالية جداً',
            'ملاحظات': 'يوم التحرير الوطني'
        },
        {
            'الفترة الزمنية': 'فترة المقاطعة الشاملة\n(1991-2003م)',
            'السنة': '1991-2003م',
            'الحدث/التطور': 'قطع العلاقات الدبلوماسية والاقتصادية بالكامل',
            'طبيعة العلاقة': 'منقطعة تماماً',
            'التفاصيل': 'قطع كامل للعلاقات الدبلوماسية والتجارية. عقوبات دولية على العراق. مطالبات كويتية بالتعويضات.',
            'التأثير': 'سلبي مستمر',
            'الأهمية': 'عالية',
            'ملاحظات': 'فترة الجمود الكامل'
        },
        {
            'الفترة الزمنية': 'سقوط النظام العراقي\n(2003م)',
            'السنة': '9 أبريل 2003م',
            'الحدث/التطور': 'سقوط نظام صدام حسين وبداية عهد جديد',
            'طبيعة العلاقة': 'بداية التطبيع الحذر',
            'التفاصيل': 'سقوط النظام العراقي السابق بعد الغزو الأمريكي. الكويت تبدي استعداداً حذراً لإعادة العلاقات مع العراق الجديد.',
            'التأثير': 'إيجابي محتمل',
            'الأهمية': 'عالية جداً',
            'ملاحظات': 'نقطة تحول تاريخية'
        },
        {
            'الفترة الزمنية': 'إعادة بناء العلاقات\n(2004-2010م)',
            'السنة': '2004-2010م',
            'الحدث/التطور': 'استئناف العلاقات الدبلوماسية والتعاون التدريجي',
            'طبيعة العلاقة': 'تطبيع تدريجي',
            'التفاصيل': 'إعادة فتح السفارات، بداية التعاون الاقتصادي المحدود، حل تدريجي للقضايا العالقة، تبادل الزيارات الرسمية.',
            'التأثير': 'إيجابي متنامي',
            'الأهمية': 'عالية',
            'ملاحظات': 'خطوات إيجابية متدرجة'
        },
        {
            'الفترة الزمنية': 'التعاون الاقتصادي المتقدم\n(2010-2020م)',
            'السنة': '2010-2020م',
            'الحدث/التطور': 'تطوير الشراكة الاقتصادية والتجارية الشاملة',
            'طبيعة العلاقة': 'شراكة اقتصادية',
            'التفاصيل': 'زيادة كبيرة في التبادل التجاري، استثمارات كويتية في العراق، تعاون في مجال الطاقة والبنية التحتية.',
            'التأثير': 'إيجابي قوي',
            'الأهمية': 'عالية',
            'ملاحظات': 'نمو اقتصادي متبادل'
        },
        {
            'الفترة الزمنية': 'الشراكة الاستراتيجية\n(2020-2025م)',
            'السنة': '2020-2025م',
            'الحدث/التطور': 'تعزيز الشراكة الاستراتيجية الشاملة',
            'طبيعة العلاقة': 'شراكة استراتيجية متقدمة',
            'التفاصيل': 'تطوير مشاريع مشتركة ضخمة، تعاون في مجالات الطاقة والتكنولوجيا والتعليم، تنسيق السياسات الإقليمية.',
            'التأثير': 'إيجابي استثنائي',
            'الأهمية': 'عالية جداً',
            'ملاحظات': 'مستقبل واعد'
        }
    ]

    # إنشاء ملف Excel
    wb = Workbook()
    ws = wb.active
    ws.title = "العلاقات الكويتية العراقية"

    # إعداد الخطوط المحسنة
    title_font = Font(name='Amiri', size=20, bold=True, color='FFFFFF')
    header_font = Font(name='Amiri', size=14, bold=True, color='FFFFFF')
    data_font = Font(name='Amiri', size=11)
    period_font = Font(name='Amiri', size=12, bold=True, color='1F4E79')

    # إعداد الألوان المحسنة
    title_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
    header_fill = PatternFill(start_color='2E4A6B', end_color='2E4A6B', fill_type='solid')

    # ألوان متدرجة حسب التأثير
    positive_strong_fill = PatternFill(start_color='C8E6C9', end_color='C8E6C9', fill_type='solid')
    positive_fill = PatternFill(start_color='E8F5E8', end_color='E8F5E8', fill_type='solid')
    negative_strong_fill = PatternFill(start_color='FFCDD2', end_color='FFCDD2', fill_type='solid')
    negative_fill = PatternFill(start_color='FFEBEE', end_color='FFEBEE', fill_type='solid')
    neutral_fill = PatternFill(start_color='FFF8E1', end_color='FFF8E1', fill_type='solid')

    # إعداد الحدود المحسنة
    thick_border = Border(
        left=Side(style='thick', color='1F4E79'),
        right=Side(style='thick', color='1F4E79'),
        top=Side(style='thick', color='1F4E79'),
        bottom=Side(style='thick', color='1F4E79')
    )

    thin_border = Border(
        left=Side(style='thin', color='666666'),
        right=Side(style='thin', color='666666'),
        top=Side(style='thin', color='666666'),
        bottom=Side(style='thin', color='666666')
    )

    return wb, ws, historical_data, title_font, header_font, data_font, period_font, title_fill, header_fill, positive_strong_fill, positive_fill, negative_strong_fill, negative_fill, neutral_fill, thick_border, thin_border

def setup_worksheet_layout(ws, title_font, header_font, data_font, title_fill, header_fill, thick_border, thin_border):
    """إعداد تخطيط ورقة العمل"""

    # إضافة العنوان الرئيسي المحسن
    ws.merge_cells('A1:H2')
    ws['A1'] = 'تطور العلاقات الكويتية العراقية عبر التاريخ\nدراسة تاريخية شاملة'
    ws['A1'].font = title_font
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    ws['A1'].fill = title_fill
    ws['A1'].border = thick_border

    # إضافة معلومات إضافية
    ws.merge_cells('A3:H3')
    ws['A3'] = f'تاريخ الإعداد: {datetime.datetime.now().strftime("%d %B %Y")} | إعداد: قسم الدراسات التاريخية'
    ws['A3'].font = Font(name='Amiri', size=12, italic=True, color='666666')
    ws['A3'].alignment = Alignment(horizontal='center', vertical='center')

    # إضافة رؤوس الأعمدة المحسنة
    headers = [
        'الفترة الزمنية',
        'التاريخ المحدد',
        'الحدث التاريخي/التطور',
        'طبيعة العلاقة',
        'التفاصيل والسياق',
        'التأثير',
        'مستوى الأهمية',
        'ملاحظات إضافية'
    ]

    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=5, column=col_num)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        cell.border = thick_border

    return headers

def add_data_to_worksheet(ws, historical_data, data_font, period_font, positive_strong_fill, positive_fill, negative_strong_fill, negative_fill, neutral_fill, thin_border):
    """إضافة البيانات إلى ورقة العمل"""

    for row_num, data_row in enumerate(historical_data, 6):
        row_data = [
            data_row['الفترة الزمنية'],
            data_row['السنة'],
            data_row['الحدث/التطور'],
            data_row['طبيعة العلاقة'],
            data_row['التفاصيل'],
            data_row['التأثير'],
            data_row['الأهمية'],
            data_row['ملاحظات']
        ]

        # تحديد لون الصف حسب التأثير
        if 'استثنائي' in str(data_row['التأثير']) or 'كارثي' in str(data_row['التأثير']):
            if 'إيجابي' in str(data_row['التأثير']):
                row_fill = positive_strong_fill
            else:
                row_fill = negative_strong_fill
        elif 'إيجابي' in str(data_row['التأثير']):
            row_fill = positive_fill
        elif 'سلبي' in str(data_row['التأثير']):
            row_fill = negative_fill
        else:
            row_fill = neutral_fill

        for col_num, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.value = value

            # تطبيق خط خاص للفترة الزمنية
            if col_num == 1:
                cell.font = period_font
            else:
                cell.font = data_font

            cell.alignment = Alignment(horizontal='right', vertical='top', wrap_text=True)
            cell.border = thin_border
            cell.fill = row_fill

    return ws.max_row

def format_columns(ws):
    """تنسيق عرض الأعمدة وارتفاع الصفوف"""

    # تعديل عرض الأعمدة
    column_widths = [20, 15, 35, 20, 50, 18, 15, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width

    # تعديل ارتفاع الصفوف
    ws.row_dimensions[1].height = 60  # العنوان الرئيسي
    ws.row_dimensions[3].height = 25  # معلومات إضافية
    ws.row_dimensions[5].height = 40  # رؤوس الأعمدة

    # ارتفاع صفوف البيانات
    for row in range(6, ws.max_row + 1):
        ws.row_dimensions[row].height = 45

def create_summary_sheet(wb, historical_data):
    """إنشاء ورقة ملخص وإحصائيات"""

    ws_summary = wb.create_sheet("الملخص والإحصائيات")

    # إعداد الخطوط والألوان
    title_font = Font(name='Amiri', size=18, bold=True, color='FFFFFF')
    header_font = Font(name='Amiri', size=14, bold=True, color='FFFFFF')
    data_font = Font(name='Amiri', size=12)

    title_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
    header_fill = PatternFill(start_color='2E4A6B', end_color='2E4A6B', fill_type='solid')

    # العنوان الرئيسي
    ws_summary.merge_cells('A1:F2')
    ws_summary['A1'] = 'ملخص وإحصائيات العلاقات الكويتية العراقية'
    ws_summary['A1'].font = title_font
    ws_summary['A1'].fill = title_fill
    ws_summary['A1'].alignment = Alignment(horizontal='center', vertical='center')

    # الفترات الرئيسية
    periods_data = [
        ['الفترة التاريخية', 'المدة الزمنية', 'طبيعة العلاقة السائدة', 'التقييم العام', 'الأحداث الرئيسية', 'التأثير طويل المدى'],
        ['العصر التأسيسي والعثماني', '1600-1918م (318 سنة)', 'تجارية مستقرة ومزدهرة', 'إيجابية ومثمرة', 'تأسيس الكويت، ازدهار التجارة البحرية', 'أسس العلاقات التاريخية'],
        ['فترة الانتداب والاستقلال', '1918-1961م (43 سنة)', 'انفصال إداري مع استمرار التواصل', 'مستقرة ومحدودة', 'الفصل الإداري، استقلال العراق', 'تشكيل الهويات الوطنية'],
        ['عهد التعاون والتحالف', '1961-1990م (29 سنة)', 'تعاون وتحالف استراتيجي', 'إيجابية قوية', 'استقلال الكويت، الدعم في الحرب الإيرانية', 'ذروة التعاون الإقليمي'],
        ['أزمة الغزو والاحتلال', '1990-1991م (7 أشهر)', 'عدائية واحتلال عسكري', 'كارثية ومدمرة', 'الغزو العراقي، تدمير البنية التحتية', 'صدمة تاريخية عميقة'],
        ['فترة القطيعة والعقوبات', '1991-2003م (12 سنة)', 'قطع كامل للعلاقات', 'سلبية ومتجمدة', 'عقوبات دولية، مطالبات بالتعويضات', 'جمود سياسي واقتصادي'],
        ['عهد التطبيع والتعاون الجديد', '2003-2025م (22 سنة)', 'تطبيع وشراكة متنامية', 'إيجابية متزايدة', 'سقوط النظام، إعادة العلاقات', 'بناء مستقبل مشترك']
    ]

    # إضافة جدول الفترات
    for row_num, row_data in enumerate(periods_data, 5):
        for col_num, value in enumerate(row_data, 1):
            cell = ws_summary.cell(row=row_num, column=col_num)
            cell.value = value
            if row_num == 5:  # رؤوس الأعمدة
                cell.font = header_font
                cell.fill = header_fill
            else:
                cell.font = data_font
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

    # إحصائيات عامة
    ws_summary.merge_cells('A13:F13')
    ws_summary['A13'] = 'الإحصائيات العامة'
    ws_summary['A13'].font = Font(name='Amiri', size=16, bold=True, color='1F4E79')
    ws_summary['A13'].alignment = Alignment(horizontal='center')

    stats_data = [
        ['المؤشر', 'القيمة', 'الوصف'],
        ['إجمالي الفترات المدروسة', '16 فترة', 'من التأسيس حتى الوقت الحاضر'],
        ['أطول فترة تعاون', '318 سنة', 'العصر العثماني (1600-1918)'],
        ['أقصر فترة أزمة', '7 أشهر', 'فترة الاحتلال (1990-1991)'],
        ['عدد الفترات الإيجابية', '10 فترات', '62.5% من إجمالي الفترات'],
        ['عدد الفترات السلبية', '4 فترات', '25% من إجمالي الفترات'],
        ['عدد الفترات المحايدة', '2 فترة', '12.5% من إجمالي الفترات']
    ]

    for row_num, row_data in enumerate(stats_data, 15):
        for col_num, value in enumerate(row_data, 1):
            cell = ws_summary.cell(row=row_num, column=col_num)
            cell.value = value
            if row_num == 15:  # رؤوس الأعمدة
                cell.font = header_font
                cell.fill = header_fill
            else:
                cell.font = data_font
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

    # تنسيق عرض الأعمدة
    column_widths = [25, 20, 30, 25, 35, 30]
    for i, width in enumerate(column_widths, 1):
        ws_summary.column_dimensions[get_column_letter(i)].width = width

    # تنسيق ارتفاع الصفوف
    for row in range(5, 12):
        ws_summary.row_dimensions[row].height = 35
    for row in range(15, 22):
        ws_summary.row_dimensions[row].height = 25

def create_timeline_sheet(wb, historical_data):
    """إنشاء ورقة الخط الزمني"""

    ws_timeline = wb.create_sheet("الخط الزمني")

    # إعداد الخطوط والألوان
    title_font = Font(name='Amiri', size=18, bold=True, color='FFFFFF')
    header_font = Font(name='Amiri', size=14, bold=True, color='FFFFFF')
    data_font = Font(name='Amiri', size=11)

    title_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
    header_fill = PatternFill(start_color='2E4A6B', end_color='2E4A6B', fill_type='solid')

    # العنوان
    ws_timeline.merge_cells('A1:D2')
    ws_timeline['A1'] = 'الخط الزمني للعلاقات الكويتية العراقية'
    ws_timeline['A1'].font = title_font
    ws_timeline['A1'].fill = title_fill
    ws_timeline['A1'].alignment = Alignment(horizontal='center', vertical='center')

    # رؤوس الأعمدة
    headers = ['التاريخ', 'الحدث', 'نوع التأثير', 'الأهمية']
    for col_num, header in enumerate(headers, 1):
        cell = ws_timeline.cell(row=4, column=col_num)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal='center', vertical='center')

    # إضافة البيانات
    for row_num, data_row in enumerate(historical_data, 5):
        timeline_data = [
            data_row['السنة'],
            data_row['الحدث/التطور'],
            data_row['التأثير'],
            data_row['الأهمية']
        ]

        for col_num, value in enumerate(timeline_data, 1):
            cell = ws_timeline.cell(row=row_num, column=col_num)
            cell.value = value
            cell.font = data_font
            cell.alignment = Alignment(horizontal='right', vertical='center', wrap_text=True)

    # تنسيق الأعمدة
    ws_timeline.column_dimensions['A'].width = 20
    ws_timeline.column_dimensions['B'].width = 50
    ws_timeline.column_dimensions['C'].width = 20
    ws_timeline.column_dimensions['D'].width = 15

def main():
    """الدالة الرئيسية"""
    print("جاري إنشاء ملف إكسل محسن للعلاقات الكويتية العراقية...")

    # إنشاء الملف والحصول على المتغيرات
    wb, ws, historical_data, title_font, header_font, data_font, period_font, title_fill, header_fill, positive_strong_fill, positive_fill, negative_strong_fill, negative_fill, neutral_fill, thick_border, thin_border = create_enhanced_kuwait_iraq_relations_excel()

    # إعداد تخطيط ورقة العمل
    headers = setup_worksheet_layout(ws, title_font, header_font, data_font, title_fill, header_fill, thick_border, thin_border)

    # إضافة البيانات
    max_row = add_data_to_worksheet(ws, historical_data, data_font, period_font, positive_strong_fill, positive_fill, negative_strong_fill, negative_fill, neutral_fill, thin_border)

    # تنسيق الأعمدة والصفوف
    format_columns(ws)

    # إنشاء ورقة الملخص
    create_summary_sheet(wb, historical_data)

    # إنشاء ورقة الخط الزمني
    create_timeline_sheet(wb, historical_data)

    # حفظ الملف
    filename = f"العلاقات_الكويتية_العراقية_محسن_{datetime.datetime.now().strftime('%Y%m%d')}.xlsx"
    wb.save(filename)

    print(f"تم إنشاء الملف المحسن بنجاح: {filename}")
    print("\nالتحسينات المضافة:")
    print("✓ خطوط عربية عالية الجودة (Amiri)")
    print("✓ تنسيق ألوان متدرج حسب التأثير")
    print("✓ بيانات تاريخية مفصلة ودقيقة")
    print("✓ تخطيط احترافي مع حدود وتنسيق محسن")
    print("✓ عمود إضافي للملاحظات")
    print("✓ ارتفاع صفوف مناسب للنص العربي")
    print("✓ عرض أعمدة محسن للقراءة المريحة")
    print("✓ ورقة ملخص وإحصائيات شاملة")
    print("✓ ورقة خط زمني مبسط")
    print("\nيحتوي الملف على 3 أوراق عمل:")
    print("1. العلاقات الكويتية العراقية - البيانات التفصيلية")
    print("2. الملخص والإحصائيات - نظرة عامة")
    print("3. الخط الزمني - تسلسل زمني مبسط")

if __name__ == "__main__":
    main()
