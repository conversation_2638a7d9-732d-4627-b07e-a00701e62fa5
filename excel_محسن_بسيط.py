#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import datetime

def create_excel():
    # البيانات
    data = [
        ['العصر التأسيسي (1600-1750م)', '1613م', 'تأسيس مدينة الكويت وبداية العلاقات التجارية', 'تجارية ودية', 'تأسيس مدينة الكويت وبداية التجارة البحرية مع موانئ البصرة العراقية', 'إيجابي قوي', 'عالية جداً'],
        ['العهد العثماني (1750-1871م)', '1750-1871م', 'ازدهار العلاقات التجارية', 'تجارية مستقرة', 'تطور شبكة تجارية واسعة بين الكويت والعراق', 'إيجابي', 'عالية'],
        ['الحماية البريطانية (1871-1918م)', '1899م', 'الكويت تحت الحماية البريطانية', 'تجارية محدودة', 'استمرار العلاقات التجارية رغم الحماية البريطانية', 'إيجابي محدود', 'متوسطة'],
        ['الانتداب البريطاني (1918-1932م)', '1918م', 'الفصل الإداري الرسمي', 'انفصال إداري', 'بريطانيا تفصل الكويت إدارياً عن ولاية البصرة', 'محايد', 'عالية جداً'],
        ['الاستقلال العراقي (1932-1961م)', '1932-1961م', 'تطوير العلاقات الدبلوماسية', 'دبلوماسية طبيعية', 'علاقات دبلوماسية وتجارية منتظمة', 'إيجابي', 'متوسطة'],
        ['استقلال الكويت (1961م)', '19 يونيو 1961م', 'استقلال الكويت والاعتراف العراقي', 'دبلوماسية رسمية', 'استقلال الكويت واعتراف العراق الرسمي', 'إيجابي قوي', 'عالية جداً'],
        ['التعاون الإقليمي (1961-1980م)', '1961-1980م', 'تطوير العلاقات الاقتصادية', 'تعاون متبادل', 'تطوير علاقات اقتصادية قوية وتعاون إقليمي', 'إيجابي', 'عالية'],
        ['الحرب العراقية الإيرانية (1980-1988م)', '22 سبتمبر 1980م', 'الدعم الكويتي للعراق', 'تحالف استراتيجي', 'دعم مالي ولوجستي ضخم للعراق', 'إيجابي استثنائي', 'عالية جداً'],
        ['التوتر المتصاعد (1988-1990م)', '1988-1990م', 'تدهور العلاقات', 'متوترة ومتدهورة', 'خلافات حول الديون والحدود وإنتاج النفط', 'سلبي متزايد', 'عالية'],
        ['الغزو والاحتلال (1990-1991م)', '2 أغسطس 1990م', 'الغزو العراقي للكويت', 'عدائية - احتلال', 'غزو واحتلال شامل للكويت', 'كارثي ومدمر', 'عالية جداً'],
        ['التحرير (1991م)', '26 فبراير 1991م', 'تحرير الكويت', 'قطع كامل للعلاقات', 'تحرير الكويت بواسطة التحالف الدولي', 'إيجابي للكويت', 'عالية جداً'],
        ['المقاطعة الشاملة (1991-2003م)', '1991-2003م', 'قطع العلاقات والعقوبات', 'منقطعة تماماً', 'قطع كامل للعلاقات وعقوبات دولية', 'سلبي مستمر', 'عالية'],
        ['سقوط النظام (2003م)', '9 أبريل 2003م', 'سقوط نظام صدام حسين', 'بداية التطبيع', 'سقوط النظام وبداية عهد جديد', 'إيجابي محتمل', 'عالية جداً'],
        ['إعادة بناء العلاقات (2004-2010م)', '2004-2010م', 'استئناف العلاقات الدبلوماسية', 'تطبيع تدريجي', 'إعادة فتح السفارات وتعاون محدود', 'إيجابي متنامي', 'عالية'],
        ['التعاون الاقتصادي (2010-2020م)', '2010-2020م', 'تطوير الشراكة الاقتصادية', 'شراكة اقتصادية', 'زيادة التبادل التجاري والاستثمارات', 'إيجابي قوي', 'عالية'],
        ['الشراكة الاستراتيجية (2020-2025م)', '2020-2025م', 'تعزيز الشراكة الشاملة', 'شراكة استراتيجية', 'مشاريع مشتركة وتعاون شامل', 'إيجابي استثنائي', 'عالية جداً']
    ]
    
    # إنشاء ملف Excel
    wb = Workbook()
    ws = wb.active
    ws.title = "العلاقات الكويتية العراقية"
    
    # الخطوط والألوان
    title_font = Font(name='Amiri', size=18, bold=True, color='FFFFFF')
    header_font = Font(name='Amiri', size=12, bold=True, color='FFFFFF')
    data_font = Font(name='Amiri', size=10)
    
    title_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
    header_fill = PatternFill(start_color='2E4A6B', end_color='2E4A6B', fill_type='solid')
    positive_fill = PatternFill(start_color='E8F5E8', end_color='E8F5E8', fill_type='solid')
    negative_fill = PatternFill(start_color='FFEBEE', end_color='FFEBEE', fill_type='solid')
    neutral_fill = PatternFill(start_color='FFF8E1', end_color='FFF8E1', fill_type='solid')
    
    # العنوان
    ws.merge_cells('A1:G2')
    ws['A1'] = 'تطور العلاقات الكويتية العراقية عبر التاريخ'
    ws['A1'].font = title_font
    ws['A1'].fill = title_fill
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    
    # التاريخ
    ws.merge_cells('A3:G3')
    ws['A3'] = f'تاريخ الإعداد: {datetime.datetime.now().strftime("%d %B %Y")}'
    ws['A3'].font = Font(name='Amiri', size=10, italic=True)
    ws['A3'].alignment = Alignment(horizontal='center')
    
    # رؤوس الأعمدة
    headers = ['الفترة الزمنية', 'التاريخ', 'الحدث', 'طبيعة العلاقة', 'التفاصيل', 'التأثير', 'الأهمية']
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=5, column=col)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    # البيانات
    for row_num, row_data in enumerate(data, 6):
        for col_num, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.value = value
            cell.font = data_font
            cell.alignment = Alignment(horizontal='right', vertical='top', wrap_text=True)
            
            # تلوين حسب التأثير
            if 'إيجابي' in str(row_data[5]):
                cell.fill = positive_fill
            elif 'سلبي' in str(row_data[5]) or 'كارثي' in str(row_data[5]):
                cell.fill = negative_fill
            else:
                cell.fill = neutral_fill
    
    # تنسيق الأعمدة
    widths = [20, 15, 30, 18, 40, 15, 12]
    for i, width in enumerate(widths, 1):
        ws.column_dimensions[get_column_letter(i)].width = width
    
    # تنسيق الصفوف
    ws.row_dimensions[1].height = 50
    ws.row_dimensions[5].height = 30
    for row in range(6, len(data) + 6):
        ws.row_dimensions[row].height = 35
    
    # حفظ الملف
    filename = f"العلاقات_الكويتية_العراقية_محسن_{datetime.datetime.now().strftime('%Y%m%d')}.xlsx"
    wb.save(filename)
    print(f"تم إنشاء الملف: {filename}")

if __name__ == "__main__":
    create_excel()
