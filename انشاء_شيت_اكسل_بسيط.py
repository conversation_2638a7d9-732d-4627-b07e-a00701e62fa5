#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import os

def create_israel_supporters_csv():
    """إنشاء ملف CSV للدول الداعمة لإسرائيل مع تمييز الدول العربية"""
    
    # البيانات - الدول الداعمة لإسرائيل
    data = [
        ["الدولة", "نوع الدعم", "مبلغ المساعدات (مليار دولار سنوياً)", "عربية"],
        ["الولايات المتحدة الأمريكية", "عسكري ومالي وسياسي", "3.8", "لا"],
        ["ألمانيا", "عسكري ومالي وسياسي", "0.5", "لا"],
        ["المملكة المتحدة", "سياسي وتجاري", "0.2", "لا"],
        ["فرنسا", "سياسي وتجاري", "0.1", "لا"],
        ["كندا", "سياسي ومالي", "0.05", "لا"],
        ["أستراليا", "سياسي وتجاري", "0.03", "لا"],
        ["هولندا", "سياسي ومالي", "0.02", "لا"],
        ["*** الإمارات العربية المتحدة ***", "تطبيع واتفاقيات تجارية", "غير محدد", "نعم"],
        ["*** البحرين ***", "تطبيع وتعاون أمني", "غير محدد", "نعم"],
        ["*** المغرب ***", "تطبيع وتعاون", "غير محدد", "نعم"],
        ["*** السودان ***", "تطبيع (سابق)", "غير محدد", "نعم"],
        ["*** مصر ***", "اتفاقية سلام وتعاون أمني", "غير محدد", "نعم"],
        ["*** الأردن ***", "اتفاقية سلام وتعاون", "غير محدد", "نعم"],
        ["إيطاليا", "سياسي وتجاري", "0.01", "لا"],
        ["إسبانيا", "سياسي محدود", "0.005", "لا"],
        ["اليابان", "تجاري وتقني", "0.02", "لا"],
        ["كوريا الجنوبية", "تجاري وتقني", "0.01", "لا"],
        ["الهند", "تجاري وتقني", "غير محدد", "لا"],
        ["البرازيل", "تجاري محدود", "غير محدد", "لا"],
        ["المكسيك", "سياسي محدود", "غير محدد", "لا"]
    ]
    
    # إنشاء ملف CSV
    filename = "الدول_الداعمة_لإسرائيل.csv"
    
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerows(data)
    
    print(f"تم إنشاء الملف بنجاح: {filename}")
    print("الدول العربية مميزة بعلامات *** في بداية ونهاية الاسم")
    print("يمكنك فتح الملف في Excel وتطبيق التنسيق المطلوب")
    
    # إنشاء ملف تعليمات للتنسيق
    instructions_file = "تعليمات_التنسيق.txt"
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write("تعليمات تنسيق شيت الإكسل:\n")
        f.write("=" * 40 + "\n\n")
        f.write("1. افتح الملف 'الدول_الداعمة_لإسرائيل.csv' في Microsoft Excel\n\n")
        f.write("2. حدد الدول العربية (المميزة بعلامات ***) وقم بتطبيق التنسيق التالي:\n")
        f.write("   - لون الخط: أحمر\n")
        f.write("   - نوع الخط: عريض\n")
        f.write("   - احذف علامات *** من أسماء الدول\n\n")
        f.write("3. قم بتنسيق الجدول:\n")
        f.write("   - أضف حدود للخلايا\n")
        f.write("   - لون خلفية الرأس: رمادي غامق\n")
        f.write("   - لون خط الرأس: أبيض\n")
        f.write("   - محاذاة النص: وسط\n\n")
        f.write("4. اضبط عرض الأعمدة حسب المحتوى\n\n")
        f.write("5. احفظ الملف بصيغة Excel (.xlsx)\n\n")
        f.write("الدول العربية في القائمة:\n")
        f.write("- الإمارات العربية المتحدة\n")
        f.write("- البحرين\n")
        f.write("- المغرب\n")
        f.write("- السودان\n")
        f.write("- مصر\n")
        f.write("- الأردن\n")
    
    print(f"تم إنشاء ملف التعليمات: {instructions_file}")
    
    return filename

if __name__ == "__main__":
    create_israel_supporters_csv()
