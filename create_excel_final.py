#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
    from openpyxl.utils import get_column_letter
    import datetime
    
    def create_enhanced_excel():
        print("بدء إنشاء ملف Excel محسن...")
        
        # البيانات المحسنة
        data = [
            ['العصر التأسيسي\n(1600-1750م)', '1613م', 'تأسيس مدينة الكويت وبداية العلاقات التجارية مع العراق', 'تجارية ودية ومزدهرة', 'تأسيس مدينة الكويت على يد آل صباح وبداية التجارة البحرية النشطة مع موانئ البصرة والفاو العراقية. ازدهار تجارة اللؤلؤ والتوابل والأقمشة', 'إيجابي قوي', 'عالية جداً'],
            ['العهد العثماني المتقدم\n(1750-1871م)', '1750-1871م', 'ازدهار العلاقات التجارية في ظل الإدارة العثمانية', 'تجارية مستقرة ومنتظمة', 'تطور شبكة تجارية واسعة بين الكويت والعراق. الكويت تصبح مركزاً تجارياً مهماً لنقل البضائع من وإلى العراق عبر الخليج العربي', 'إيجابي', 'عالية'],
            ['العهد العثماني المتأخر\n(1871-1918م)', '1899م', 'الكويت تحت الحماية البريطانية مع استمرار العلاقات مع العراق', 'تجارية محدودة', 'رغم الحماية البريطانية للكويت عام 1899، استمرت العلاقات التجارية والثقافية مع العراق. تأثر متبادل في الثقافة والتجارة', 'إيجابي محدود', 'متوسطة'],
            ['فترة الانتداب البريطاني\n(1918-1932م)', '1918م', 'الفصل الإداري الرسمي بين الكويت والعراق', 'انفصال إداري', 'بريطانيا تفصل الكويت إدارياً عن ولاية البصرة العراقية وتجعلها محمية بريطانية منفصلة. إنشاء حدود سياسية واضحة', 'محايد', 'عالية جداً'],
            ['عهد الاستقلال العراقي\n(1932-1961م)', '1932-1961م', 'استقلال العراق وتطوير العلاقات الدبلوماسية مع الكويت', 'دبلوماسية طبيعية', 'بعد استقلال العراق عام 1932، تطورت علاقات دبلوماسية طبيعية مع الكويت. تبادل تجاري وثقافي منتظم', 'إيجابي', 'متوسطة'],
            ['استقلال الكويت\n(1961م)', '19 يونيو 1961م', 'استقلال الكويت والاعتراف العراقي الرسمي', 'دبلوماسية رسمية', 'استقلال دولة الكويت وإنهاء الحماية البريطانية. العراق يعترف رسمياً بالكويت كدولة مستقلة ذات سيادة', 'إيجابي قوي', 'عالية جداً'],
            ['عهد التعاون الإقليمي\n(1961-1980م)', '1961-1980م', 'تطوير العلاقات الاقتصادية والسياسية', 'تعاون متبادل', 'تطوير علاقات اقتصادية قوية، تبادل تجاري متزايد، تعاون في المنظمات العربية والإسلامية. الكويت تدعم العراق في قضايا إقليمية', 'إيجابي', 'عالية'],
            ['الحرب العراقية الإيرانية\n(1980-1988م)', '22 سبتمبر 1980م', 'الدعم الكويتي الكامل للعراق في حربه ضد إيران', 'تحالف استراتيجي', 'الكويت تقدم دعماً مالياً ولوجستياً ضخماً للعراق يقدر بمليارات الدولارات. فتح الموانئ الكويتية للتجارة العراقية', 'إيجابي استثنائي', 'عالية جداً'],
            ['فترة التوتر المتصاعد\n(1988-1990م)', '1988-1990م', 'تدهور العلاقات بسبب الخلافات الاقتصادية والحدودية', 'متوترة ومتدهورة', 'خلافات حول سداد ديون الحرب، نزاعات حدودية حول حقول النفط، اتهامات عراقية للكويت بالإفراط في إنتاج النفط', 'سلبي متزايد', 'عالية'],
            ['الغزو والاحتلال\n(1990-1991م)', '2 أغسطس 1990م', 'الغزو العراقي الشامل للكويت واحتلالها', 'عدائية - احتلال عسكري', 'القوات العراقية تغزو وتحتل الكويت بالكامل. تدمير البنية التحتية، نهب الممتلكات، انتهاكات حقوق الإنسان، وإحراق آبار النفط', 'كارثي ومدمر', 'عالية جداً'],
            ['التحرير والانتصار\n(1991م)', '26 فبراير 1991م', 'تحرير الكويت من الاحتلال العراقي', 'قطع كامل للعلاقات', 'تحرير الكويت بواسطة التحالف الدولي بقيادة الولايات المتحدة. انسحاب القوات العراقية وإحراق آبار النفط', 'إيجابي للكويت', 'عالية جداً'],
            ['فترة المقاطعة الشاملة\n(1991-2003م)', '1991-2003م', 'قطع العلاقات الدبلوماسية والاقتصادية بالكامل', 'منقطعة تماماً', 'قطع كامل للعلاقات الدبلوماسية والتجارية. عقوبات دولية على العراق. مطالبات كويتية بالتعويضات', 'سلبي مستمر', 'عالية'],
            ['سقوط النظام العراقي\n(2003م)', '9 أبريل 2003م', 'سقوط نظام صدام حسين وبداية عهد جديد', 'بداية التطبيع الحذر', 'سقوط النظام العراقي السابق بعد الغزو الأمريكي. الكويت تبدي استعداداً حذراً لإعادة العلاقات مع العراق الجديد', 'إيجابي محتمل', 'عالية جداً'],
            ['إعادة بناء العلاقات\n(2004-2010م)', '2004-2010م', 'استئناف العلاقات الدبلوماسية والتعاون التدريجي', 'تطبيع تدريجي', 'إعادة فتح السفارات، بداية التعاون الاقتصادي المحدود، حل تدريجي للقضايا العالقة، تبادل الزيارات الرسمية', 'إيجابي متنامي', 'عالية'],
            ['التعاون الاقتصادي المتقدم\n(2010-2020م)', '2010-2020م', 'تطوير الشراكة الاقتصادية والتجارية الشاملة', 'شراكة اقتصادية', 'زيادة كبيرة في التبادل التجاري، استثمارات كويتية في العراق، تعاون في مجال الطاقة والبنية التحتية', 'إيجابي قوي', 'عالية'],
            ['الشراكة الاستراتيجية\n(2020-2025م)', '2020-2025م', 'تعزيز الشراكة الاستراتيجية الشاملة', 'شراكة استراتيجية متقدمة', 'تطوير مشاريع مشتركة ضخمة، تعاون في مجالات الطاقة والتكنولوجيا والتعليم، تنسيق السياسات الإقليمية', 'إيجابي استثنائي', 'عالية جداً']
        ]
        
        # إنشاء ملف Excel
        wb = Workbook()
        ws = wb.active
        ws.title = "العلاقات الكويتية العراقية"
        
        print("إعداد التنسيقات...")
        
        # الخطوط والألوان
        title_font = Font(name='Amiri', size=20, bold=True, color='FFFFFF')
        header_font = Font(name='Amiri', size=14, bold=True, color='FFFFFF')
        data_font = Font(name='Amiri', size=11)
        period_font = Font(name='Amiri', size=12, bold=True, color='1F4E79')
        
        title_fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
        header_fill = PatternFill(start_color='2E4A6B', end_color='2E4A6B', fill_type='solid')
        positive_fill = PatternFill(start_color='E8F5E8', end_color='E8F5E8', fill_type='solid')
        negative_fill = PatternFill(start_color='FFEBEE', end_color='FFEBEE', fill_type='solid')
        neutral_fill = PatternFill(start_color='FFF8E1', end_color='FFF8E1', fill_type='solid')
        
        # الحدود
        thick_border = Border(
            left=Side(style='thick', color='1F4E79'),
            right=Side(style='thick', color='1F4E79'),
            top=Side(style='thick', color='1F4E79'),
            bottom=Side(style='thick', color='1F4E79')
        )
        
        thin_border = Border(
            left=Side(style='thin', color='666666'),
            right=Side(style='thin', color='666666'),
            top=Side(style='thin', color='666666'),
            bottom=Side(style='thin', color='666666')
        )
        
        print("إضافة العنوان والرؤوس...")
        
        # العنوان الرئيسي
        ws.merge_cells('A1:G2')
        ws['A1'] = 'تطور العلاقات الكويتية العراقية عبر التاريخ\nدراسة تاريخية شاملة ومفصلة'
        ws['A1'].font = title_font
        ws['A1'].fill = title_fill
        ws['A1'].alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        ws['A1'].border = thick_border
        
        # التاريخ
        ws.merge_cells('A3:G3')
        ws['A3'] = f'تاريخ الإعداد: {datetime.datetime.now().strftime("%d %B %Y")} | إعداد: قسم الدراسات التاريخية'
        ws['A3'].font = Font(name='Amiri', size=12, italic=True, color='666666')
        ws['A3'].alignment = Alignment(horizontal='center', vertical='center')
        
        # رؤوس الأعمدة
        headers = ['الفترة التاريخية', 'التاريخ المحدد', 'الحدث/التطور', 'طبيعة العلاقة', 'التفاصيل والسياق', 'التأثير', 'مستوى الأهمية']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=5, column=col)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            cell.border = thick_border
        
        print("إضافة البيانات...")
        
        # البيانات
        for row_num, row_data in enumerate(data, 6):
            for col_num, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_num, column=col_num)
                cell.value = value
                
                if col_num == 1:  # الفترة الزمنية
                    cell.font = period_font
                else:
                    cell.font = data_font
                
                cell.alignment = Alignment(horizontal='right', vertical='top', wrap_text=True)
                cell.border = thin_border
                
                # تلوين حسب التأثير
                impact = row_data[5]  # عمود التأثير
                if 'إيجابي' in impact:
                    cell.fill = positive_fill
                elif 'سلبي' in impact or 'كارثي' in impact:
                    cell.fill = negative_fill
                else:
                    cell.fill = neutral_fill
        
        print("تنسيق الأعمدة والصفوف...")
        
        # تنسيق عرض الأعمدة
        column_widths = [22, 18, 35, 22, 50, 18, 15]
        for i, width in enumerate(column_widths, 1):
            ws.column_dimensions[get_column_letter(i)].width = width
        
        # تنسيق ارتفاع الصفوف
        ws.row_dimensions[1].height = 60  # العنوان
        ws.row_dimensions[3].height = 25  # التاريخ
        ws.row_dimensions[5].height = 40  # الرؤوس
        
        for row in range(6, len(data) + 6):
            ws.row_dimensions[row].height = 50
        
        # حفظ الملف
        filename = f"العلاقات_الكويتية_العراقية_محسن_نهائي_{datetime.datetime.now().strftime('%Y%m%d')}.xlsx"
        wb.save(filename)
        
        print(f"✅ تم إنشاء الملف بنجاح: {filename}")
        print("\n🎯 التحسينات المضافة:")
        print("✓ خطوط عربية عالية الجودة (Amiri)")
        print("✓ تنسيق ألوان متدرج حسب التأثير")
        print("✓ بيانات تاريخية مفصلة ودقيقة (16 فترة)")
        print("✓ تخطيط احترافي مع حدود وتنسيق محسن")
        print("✓ ارتفاع صفوف مناسب للنص العربي الطويل")
        print("✓ عرض أعمدة محسن للقراءة المريحة")
        print("✓ تفاصيل شاملة لكل فترة تاريخية")
        
        return filename
    
    if __name__ == "__main__":
        create_enhanced_excel()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("يرجى تثبيت openpyxl باستخدام: pip install openpyxl")
except Exception as e:
    print(f"❌ خطأ في إنشاء الملف: {e}")
    print("تأكد من أن لديك صلاحيات الكتابة في المجلد الحالي")
