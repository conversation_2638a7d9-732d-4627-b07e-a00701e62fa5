from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_BREAK
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn
import os

def create_uae_travel_word_document():
    # إنشاء مستند ورد جديد
    doc = Document()

    # تعيين اتجاه النص من اليمين لليسار
    sections = doc.sections
    for section in sections:
        section.page_height = Inches(11.69)  # A4
        section.page_width = Inches(8.27)
        section.left_margin = Inches(1)
        section.right_margin = Inches(1)
        section.top_margin = Inches(1)
        section.bottom_margin = Inches(1)

    # العنوان الرئيسي
    title = doc.add_heading('دليل شامل لسفر العراقيين إلى دولة الإمارات العربية المتحدة', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    title_run = title.runs[0]
    title_run.font.name = 'Arabic Typesetting'
    title_run.font.size = Pt(24)
    title_run.font.color.rgb = RGBColor(31, 78, 121)
    title_run.bold = True

    # إضافة فاصل
    doc.add_paragraph()

    # القسم الأول: شروط السفر والتأشيرة
    heading1 = doc.add_heading('أولاً: شروط السفر والتأشيرة', level=1)
    heading1.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    heading1_run = heading1.runs[0]
    heading1_run.font.name = 'Arabic Typesetting'
    heading1_run.font.size = Pt(18)
    heading1_run.font.color.rgb = RGBColor(31, 78, 121)

    # جدول شروط السفر
    table1 = doc.add_table(rows=1, cols=5)
    table1.alignment = WD_TABLE_ALIGNMENT.CENTER
    table1.style = 'Table Grid'

    # رؤوس الجدول
    headers1 = ['نوع التأشيرة', 'المدة', 'الرسوم (درهم)', 'الوثائق المطلوبة', 'ملاحظات']
    header_cells1 = table1.rows[0].cells
    for i, header in enumerate(headers1):
        header_cells1[i].text = header
        header_cells1[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = header_cells1[i].paragraphs[0].runs[0]
        run.font.name = 'Arabic Typesetting'
        run.font.size = Pt(12)
        run.bold = True
        run.font.color.rgb = RGBColor(255, 255, 255)
        # تلوين خلفية الرأس
        shading_elm = OxmlElement('w:shd')
        shading_elm.set(qn('w:fill'), '1F4E79')
        header_cells1[i]._tc.get_or_add_tcPr().append(shading_elm)

    # بيانات الجدول
    travel_data = [
        ['تأشيرة سياحية 30 يوم', '30 يوم', '350', 'جواز سفر صالح 6 أشهر، صورة شخصية، حجز فندق، تذكرة طيران', 'قابلة للتمديد مرة واحدة'],
        ['تأشيرة سياحية 90 يوم', '90 يوم', '650', 'جواز سفر صالح 6 أشهر، صورة شخصية، حجز فندق، تذكرة طيران', 'للسياحة طويلة المدى'],
        ['تأشيرة ترانزيت', '96 ساعة', '60', 'جواز سفر، تذكرة طيران للوجهة النهائية', 'للمسافرين عبر الإمارات'],
        ['تأشيرة دخول متعددة', '30 يوم لكل دخول', '1000', 'جواز سفر، كفيل في الإمارات، ضمان مالي', 'صالحة لسنة واحدة']
    ]

    for row_data in travel_data:
        row_cells = table1.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = cell_data
            row_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            run = row_cells[i].paragraphs[0].runs[0]
            run.font.name = 'Arabic Typesetting'
            run.font.size = Pt(11)

    doc.add_paragraph()

    # القسم الثاني: أنواع الإقامة
    heading2 = doc.add_heading('ثانياً: أنواع الإقامة في دولة الإمارات', level=1)
    heading2.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    heading2_run = heading2.runs[0]
    heading2_run.font.name = 'Arabic Typesetting'
    heading2_run.font.size = Pt(18)
    heading2_run.font.color.rgb = RGBColor(31, 78, 121)

    # جدول أنواع الإقامة
    table2 = doc.add_table(rows=1, cols=6)
    table2.alignment = WD_TABLE_ALIGNMENT.CENTER
    table2.style = 'Table Grid'

    # رؤوس الجدول
    headers2 = ['نوع الإقامة', 'المدة', 'الشروط الأساسية', 'الوثائق المطلوبة', 'الرسوم (درهم)', 'المزايا']
    header_cells2 = table2.rows[0].cells
    for i, header in enumerate(headers2):
        header_cells2[i].text = header
        header_cells2[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = header_cells2[i].paragraphs[0].runs[0]
        run.font.name = 'Arabic Typesetting'
        run.font.size = Pt(12)
        run.bold = True
        run.font.color.rgb = RGBColor(255, 255, 255)
        # تلوين خلفية الرأس
        shading_elm = OxmlElement('w:shd')
        shading_elm.set(qn('w:fill'), '1F4E79')
        header_cells2[i]._tc.get_or_add_tcPr().append(shading_elm)

    # بيانات أنواع الإقامة
    residence_data = [
        ['إقامة عمل', '2-3 سنوات', 'عقد عمل مع شركة مرخصة', 'جواز سفر، شهادات، عقد عمل، فحص طبي', '3000-5000', 'حق العمل، كفالة الأسرة'],
        ['إقامة استثمار', '2-3 سنوات', 'استثمار 500 ألف درهم', 'جواز سفر، إثبات الاستثمار، كشف حساب', '10000', 'حرية الاستثمار والتجارة'],
        ['الإقامة الذهبية', '5-10 سنوات', 'استثمار عقاري 2 مليون درهم', 'جواز سفر، إثبات الاستثمار، تقييم عقاري', '25000', 'إقامة طويلة المدى، مزايا خاصة'],
        ['إقامة طالب', 'سنة دراسية', 'قبول جامعي معتمد', 'جواز سفر، قبول جامعي، كشف درجات', '2000', 'حق الدراسة، عمل جزئي'],
        ['إقامة تقاعد', '5 سنوات', 'عمر 55+ ودخل ثابت', 'جواز سفر، إثبات دخل، تأمين صحي', '15000', 'إقامة مريحة للمتقاعدين']
    ]

    for row_data in residence_data:
        row_cells = table2.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = cell_data
            row_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
            run = row_cells[i].paragraphs[0].runs[0]
            run.font.name = 'Arabic Typesetting'
            run.font.size = Pt(11)

    doc.add_page_break()

    # القسم الثالث: الوثائق المطلوبة
    heading3 = doc.add_heading('ثالثاً: الوثائق المطلوبة للسفر والإقامة', level=1)
    heading3.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    heading3_run = heading3.runs[0]
    heading3_run.font.name = 'Arabic Typesetting'
    heading3_run.font.size = Pt(18)
    heading3_run.font.color.rgb = RGBColor(31, 78, 121)

    # جدول الوثائق
    table3 = doc.add_table(rows=1, cols=4)
    table3.alignment = WD_TABLE_ALIGNMENT.CENTER
    table3.style = 'Table Grid'

    # رؤوس الجدول
    headers3 = ['نوع الوثيقة', 'الغرض', 'الشروط', 'ملاحظات']
    header_cells3 = table3.rows[0].cells
    for i, header in enumerate(headers3):
        header_cells3[i].text = header
        header_cells3[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = header_cells3[i].paragraphs[0].runs[0]
        run.font.name = 'Arabic Typesetting'
        run.font.size = Pt(12)
        run.bold = True
        run.font.color.rgb = RGBColor(255, 255, 255)
        # تلوين خلفية الرأس
        shading_elm = OxmlElement('w:shd')
        shading_elm.set(qn('w:fill'), '1F4E79')
        header_cells3[i]._tc.get_or_add_tcPr().append(shading_elm)

    # بيانات الوثائق
    documents_data = [
        ['جواز السفر العراقي', 'السفر والإقامة', 'صالح لمدة 6 أشهر على الأقل', 'يجب أن يحتوي على صفحات فارغة'],
        ['صورة شخصية', 'طلب التأشيرة', 'خلفية بيضاء، مقاس 4×6', 'حديثة لا تزيد عن 6 أشهر'],
        ['حجز الفندق', 'تأشيرة سياحية', 'حجز مؤكد ومدفوع', 'يمكن الإلغاء بعد الحصول على التأشيرة'],
        ['تذكرة الطيران', 'تأشيرة سياحية', 'ذهاب وإياب', 'يفضل الحجز القابل للتغيير'],
        ['كشف حساب بنكي', 'إثبات القدرة المالية', 'آخر 3 أشهر', 'رصيد لا يقل عن 10000 درهم'],
        ['شهادة عدم محكومية', 'إقامة عمل', 'مصدقة من الخارجية العراقية', 'صالحة لمدة 6 أشهر'],
        ['الشهادات العلمية', 'إقامة عمل/طالب', 'مصدقة ومترجمة', 'معادلة من وزارة التعليم الإماراتية'],
        ['فحص طبي', 'إقامة عمل', 'من مراكز معتمدة', 'يشمل فحص الأمراض المعدية']
    ]

    for row_data in documents_data:
        row_cells = table3.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = cell_data
            row_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
            run = row_cells[i].paragraphs[0].runs[0]
            run.font.name = 'Arabic Typesetting'
            run.font.size = Pt(11)

    doc.add_paragraph()

    # القسم الرابع: خطوات التقديم
    heading4 = doc.add_heading('رابعاً: خطوات التقديم للحصول على التأشيرة والإقامة', level=1)
    heading4.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    heading4_run = heading4.runs[0]
    heading4_run.font.name = 'Arabic Typesetting'
    heading4_run.font.size = Pt(18)
    heading4_run.font.color.rgb = RGBColor(31, 78, 121)

    # قائمة الخطوات
    steps_data = [
        "تجهيز الوثائق المطلوبة وترجمتها وتصديقها (1-2 أسبوع)",
        "تقديم طلب التأشيرة عبر الموقع الرسمي أو مكاتب السفر (1 يوم)",
        "دفع الرسوم المطلوبة (فوري)",
        "انتظار الموافقة على التأشيرة (3-7 أيام عمل)",
        "طباعة التأشيرة والسفر إلى الإمارات (فوري)",
        "الدخول إلى الإمارات وختم الجواز (عند الوصول)",
        "تقديم طلب الإقامة إن أردت (خلال 30 يوم من الدخول)",
        "إجراء الفحص الطبي (1-3 أيام)",
        "الحصول على الهوية الإماراتية (7-14 يوم عمل)"
    ]

    for i, step in enumerate(steps_data, 1):
        p = doc.add_paragraph()
        p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        run = p.add_run(f"{i}. {step}")
        run.font.name = 'Arabic Typesetting'
        run.font.size = Pt(12)
        p.paragraph_format.space_after = Pt(6)

    doc.add_page_break()

    # القسم الخامس: معلومات مهمة ونصائح
    heading5 = doc.add_heading('خامساً: معلومات مهمة ونصائح للعراقيين في الإمارات', level=1)
    heading5.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    heading5_run = heading5.runs[0]
    heading5_run.font.name = 'Arabic Typesetting'
    heading5_run.font.size = Pt(18)
    heading5_run.font.color.rgb = RGBColor(31, 78, 121)

    # معلومات مهمة
    tips_sections = [
        ("🚨 أرقام الطوارئ", [
            "الشرطة: 999",
            "الإسعاف: 998",
            "الدفاع المدني: 997"
        ]),
        ("💰 العملة والمال", [
            "الدرهم الإماراتي (AED) = 100 فلس",
            "1 دولار أمريكي = 3.67 درهم تقريباً",
            "ضريبة القيمة المضافة 5%",
            "لا توجد ضريبة دخل شخصي"
        ]),
        ("🌡️ المناخ والطقس", [
            "حار صيفاً (40-50 درجة مئوية)",
            "معتدل شتاءً (15-25 درجة مئوية)",
            "أفضل وقت للزيارة: نوفمبر - مارس",
            "رطوبة عالية في الصيف"
        ]),
        ("🚌 المواصلات", [
            "مترو دبي وأبوظبي",
            "حافلات عامة منتظمة",
            "تاكسي، أوبر، كريم",
            "بطاقة نول للمواصلات العامة"
        ]),
        ("🏥 التأمين الصحي", [
            "إجباري لجميع المقيمين",
            "يغطي العلاج الأساسي",
            "مستشفيات حكومية وخاصة",
            "خدمات طبية عالية الجودة"
        ]),
        ("🏦 البنوك والحسابات", [
            "يتطلب إقامة وراتب 3000 درهم",
            "البنوك الرئيسية: الإمارات، أدنوك، راك",
            "خدمات مصرفية إلكترونية متطورة",
            "بطاقات ائتمان وخصم متنوعة"
        ]),
        ("🏠 السكن", [
            "شقق، فيلل، غرف مشتركة",
            "الإيجار يدفع بشيكات مؤجلة",
            "عقود إيجار سنوية عادة",
            "مناطق مختلفة بأسعار متفاوتة"
        ]),
        ("📚 التعليم", [
            "مدارس حكومية ومدارس خاصة",
            "التعليم الحكومي مجاني للمواطنين",
            "جامعات محلية وفروع دولية",
            "نظام تعليمي متطور"
        ]),
        ("🛍️ التسوق والترفيه", [
            "مولات حديثة ومتطورة",
            "أسواق تقليدية (الذهب، التوابل)",
            "مهرجان التسوق في يناير-فبراير",
            "فعاليات ترفيهية متنوعة"
        ]),
        ("📱 الإنترنت والاتصالات", [
            "شركات: اتصالات، دو، فيرجن موبايل",
            "سرعة إنترنت عالية جداً",
            "تغطية ممتازة في جميع الإمارات",
            "خدمات 5G متوفرة"
        ]),
        ("🕌 الثقافة والتقاليد", [
            "احترام التقاليد الإسلامية والعربية",
            "اللباس المحتشم في الأماكن العامة",
            "احترام أوقات الصلاة",
            "تنوع ثقافي كبير"
        ]),
        ("⚖️ القوانين المهمة", [
            "منع الكحول في الأماكن العامة",
            "احترام شهر رمضان المبارك",
            "عقوبات صارمة للمخالفات",
            "قوانين مرور صارمة"
        ])
    ]

    for section_title, section_items in tips_sections:
        # عنوان فرعي
        sub_heading = doc.add_heading(section_title, level=2)
        sub_heading.alignment = WD_ALIGN_PARAGRAPH.RIGHT
        sub_heading_run = sub_heading.runs[0]
        sub_heading_run.font.name = 'Arabic Typesetting'
        sub_heading_run.font.size = Pt(14)
        sub_heading_run.font.color.rgb = RGBColor(0, 102, 204)

        # العناصر
        for item in section_items:
            p = doc.add_paragraph()
            p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            run = p.add_run(f"• {item}")
            run.font.name = 'Arabic Typesetting'
            run.font.size = Pt(11)
            p.paragraph_format.space_after = Pt(3)

        doc.add_paragraph()  # مسافة بين الأقسام

    doc.add_page_break()

    # القسم السادس: جهات الاتصال
    heading6 = doc.add_heading('سادساً: جهات الاتصال المهمة للعراقيين في الإمارات', level=1)
    heading6.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    heading6_run = heading6.runs[0]
    heading6_run.font.name = 'Arabic Typesetting'
    heading6_run.font.size = Pt(18)
    heading6_run.font.color.rgb = RGBColor(31, 78, 121)

    # جدول جهات الاتصال
    table6 = doc.add_table(rows=1, cols=4)
    table6.alignment = WD_TABLE_ALIGNMENT.CENTER
    table6.style = 'Table Grid'

    # رؤوس الجدول
    headers6 = ['الجهة', 'العنوان', 'الهاتف', 'الخدمات']
    header_cells6 = table6.rows[0].cells
    for i, header in enumerate(headers6):
        header_cells6[i].text = header
        header_cells6[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.CENTER
        run = header_cells6[i].paragraphs[0].runs[0]
        run.font.name = 'Arabic Typesetting'
        run.font.size = Pt(12)
        run.bold = True
        run.font.color.rgb = RGBColor(255, 255, 255)
        # تلوين خلفية الرأس
        shading_elm = OxmlElement('w:shd')
        shading_elm.set(qn('w:fill'), '1F4E79')
        header_cells6[i]._tc.get_or_add_tcPr().append(shading_elm)

    # بيانات جهات الاتصال
    contacts_data = [
        ['القنصلية العراقية - دبي', 'منطقة الجميرا، دبي', '+971-4-3971717', 'خدمات قنصلية، تجديد جوازات'],
        ['السفارة العراقية - أبوظبي', 'منطقة الخالدية، أبوظبي', '+971-2-4446633', 'خدمات دبلوماسية'],
        ['الهجرة والجوازات - دبي', 'مطار دبي الدولي', '+971-4-3131111', 'تأشيرات، إقامات'],
        ['وزارة الموارد البشرية', 'مختلف الإمارات', '600-590000', 'تصاريح عمل، شكاوى عمالية'],
        ['هيئة الصحة - دبي', 'مختلف المناطق', '+971-4-3370000', 'خدمات صحية، تأمين'],
        ['شرطة دبي', 'جميع أنحاء دبي', '999', 'طوارئ، خدمات أمنية'],
        ['بلدية دبي', 'مختلف المناطق', '+971-4-2218888', 'خدمات بلدية، تراخيص'],
        ['مؤسسة محمد بن راشد للإسكان', 'دبي', '+971-4-2222444', 'خدمات إسكان'],
        ['هيئة كهرباء ومياه دبي', 'جميع أنحاء دبي', '991', 'خدمات الكهرباء والمياه']
    ]

    for row_data in contacts_data:
        row_cells = table6.add_row().cells
        for i, cell_data in enumerate(row_data):
            row_cells[i].text = cell_data
            row_cells[i].paragraphs[0].alignment = WD_ALIGN_PARAGRAPH.RIGHT
            run = row_cells[i].paragraphs[0].runs[0]
            run.font.name = 'Arabic Typesetting'
            run.font.size = Pt(11)

    # خاتمة
    doc.add_paragraph()
    conclusion = doc.add_paragraph()
    conclusion.alignment = WD_ALIGN_PARAGRAPH.CENTER
    conclusion_run = conclusion.add_run("تم إعداد هذا الدليل بعناية لمساعدة الأشقاء العراقيين في رحلتهم إلى الإمارات\nنتمنى لكم رحلة موفقة وإقامة سعيدة!")
    conclusion_run.font.name = 'Arabic Typesetting'
    conclusion_run.font.size = Pt(14)
    conclusion_run.font.color.rgb = RGBColor(31, 78, 121)
    conclusion_run.bold = True

    # تاريخ التحديث
    doc.add_paragraph()
    date_para = doc.add_paragraph()
    date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    date_run = date_para.add_run("آخر تحديث: يناير 2025")
    date_run.font.name = 'Arabic Typesetting'
    date_run.font.size = Pt(12)
    date_run.italic = True

    # حفظ الملف
    filename = "دليل_سفر_العراقيين_الى_الامارات_2024.docx"
    doc.save(filename)
    print(f"تم إنشاء ملف الورد بنجاح: {filename}")

    return filename

if __name__ == "__main__":
    create_uae_travel_word_document()
