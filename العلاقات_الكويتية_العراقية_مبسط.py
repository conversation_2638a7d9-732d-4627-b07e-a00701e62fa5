#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف CSV للعلاقات الكويتية العراقية عبر التاريخ
"""

import csv
import datetime

def create_kuwait_iraq_relations_csv():
    """إنشاء ملف CSV شامل للعلاقات الكويتية العراقية"""
    
    # البيانات التاريخية للعلاقات الكويتية العراقية
    historical_data = [
        ['الفترة الزمنية', 'السنة', 'الحدث/التطور', 'طبيعة العلاقة', 'التفاصيل', 'التأثير', 'الأهمية'],
        ['1600-1750', '1600-1750', 'تأسيس الكويت وبداية العلاقات التجارية', 'تجارية ودية', 'تأسيس مدينة الكويت وبداية التجارة البحرية مع موانئ البصرة العراقية', 'إيجابي', 'عالية'],
        ['1750-1900', '1750-1900', 'العلاقات التجارية في العهد العثماني', 'تجارية مستقرة', 'ازدهار التجارة البحرية بين الكويت والعراق تحت الحكم العثماني', 'إيجابي', 'عالية'],
        ['1900-1920', '1918', 'انفصال الكويت عن العراق إدارياً', 'تغيير إداري', 'بريطانيا تفصل الكويت إدارياً عن العراق وتجعلها محمية بريطانية منفصلة', 'محايد', 'عالية جداً'],
        ['1920-1960', '1920-1960', 'العلاقات في عهد الانتداب والاستقلال', 'دبلوماسية مستقرة', 'علاقات دبلوماسية وتجارية طبيعية بين البلدين', 'إيجابي', 'متوسطة'],
        ['1960-1980', '1961', 'استقلال الكويت والاعتراف العراقي', 'دبلوماسية رسمية', 'استقلال الكويت واعتراف العراق بها كدولة مستقلة', 'إيجابي', 'عالية جداً'],
        ['1980-1988', '1980-1988', 'الحرب العراقية الإيرانية والدعم الكويتي', 'تحالف وداعمة', 'الكويت تدعم العراق مالياً وسياسياً في حربه ضد إيران', 'إيجابي قوي', 'عالية جداً'],
        ['1988-1990', '1988-1990', 'توتر العلاقات والخلافات الحدودية', 'متوترة', 'خلافات حول الديون والحدود وإنتاج النفط', 'سلبي', 'عالية'],
        ['1990', '2 أغسطس 1990', 'الغزو العراقي للكويت', 'عدائية - احتلال', 'القوات العراقية تغزو وتحتل الكويت بالكامل', 'سلبي جداً', 'عالية جداً'],
        ['1991', '26 فبراير 1991', 'تحرير الكويت', 'قطع العلاقات', 'تحرير الكويت من الاحتلال العراقي بعد حرب الخليج الثانية', 'إيجابي للكويت', 'عالية جداً'],
        ['1991-2003', '1991-2003', 'قطع العلاقات والعقوبات الدولية', 'منقطعة', 'قطع كامل للعلاقات الدبلوماسية والاقتصادية', 'سلبي', 'عالية'],
        ['2003', '9 أبريل 2003', 'سقوط نظام صدام حسين', 'بداية التطبيع', 'سقوط النظام العراقي السابق وبداية عهد جديد', 'إيجابي', 'عالية جداً'],
        ['2004-2010', '2004-2010', 'استئناف العلاقات الدبلوماسية تدريجياً', 'تطبيع تدريجي', 'إعادة فتح السفارات وبداية التعاون الاقتصادي المحدود', 'إيجابي', 'عالية'],
        ['2010-2020', '2010-2020', 'تطوير العلاقات الاقتصادية والتجارية', 'تعاون اقتصادي', 'زيادة التبادل التجاري والاستثمارات المتبادلة', 'إيجابي', 'عالية'],
        ['2020-2025', '2020-2025', 'تعزيز الشراكة الاستراتيجية', 'شراكة استراتيجية', 'تطوير المشاريع المشتركة والتعاون في مختلف المجالات', 'إيجابي قوي', 'عالية جداً']
    ]
    
    # إنشاء ملف CSV
    filename = f"العلاقات_الكويتية_العراقية_{datetime.datetime.now().strftime('%Y%m%d')}.csv"
    
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)
        
        # كتابة العنوان
        writer.writerow(['تطور العلاقات الكويتية العراقية عبر التاريخ'])
        writer.writerow([f'تاريخ الإنشاء: {datetime.datetime.now().strftime("%Y-%m-%d")}'])
        writer.writerow([])  # سطر فارغ
        
        # كتابة البيانات
        for row in historical_data:
            writer.writerow(row)
    
    return filename

def create_summary_csv():
    """إنشاء ملف ملخص منفصل"""
    
    summary_data = [
        ['ملخص تطور العلاقات الكويتية العراقية'],
        [f'تاريخ الإنشاء: {datetime.datetime.now().strftime("%Y-%m-%d")}'],
        [],
        ['الفترة', 'الوصف', 'طبيعة العلاقة', 'التقييم'],
        ['1600-1918', 'العهد العثماني', 'تجارية مستقرة', 'إيجابية'],
        ['1918-1961', 'فترة الانتداب', 'دبلوماسية عادية', 'مستقرة'],
        ['1961-1990', 'ما بعد الاستقلال', 'تحالف ثم توتر', 'متقلبة'],
        ['1990-1991', 'الغزو والتحرير', 'عدائية', 'سلبية جداً'],
        ['1991-2003', 'قطع العلاقات', 'منقطعة', 'سلبية'],
        ['2003-الآن', 'التطبيع والتعاون', 'شراكة متنامية', 'إيجابية متزايدة']
    ]
    
    filename = f"ملخص_العلاقات_الكويتية_العراقية_{datetime.datetime.now().strftime('%Y%m%d')}.csv"
    
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)
        for row in summary_data:
            writer.writerow(row)
    
    return filename

def create_detailed_analysis():
    """إنشاء تحليل مفصل للعلاقات"""
    
    analysis_text = f"""
تحليل مفصل للعلاقات الكويتية العراقية عبر التاريخ
تاريخ الإعداد: {datetime.datetime.now().strftime('%Y-%m-%d')}

المراحل الرئيسية:

1. المرحلة التأسيسية (1600-1918):
   - تأسيس الكويت كمركز تجاري مهم
   - علاقات تجارية وثيقة مع العراق
   - الاستفادة من الموقع الجغرافي الاستراتيجي

2. مرحلة الانتداب البريطاني (1918-1961):
   - فصل إداري بين الكويت والعراق
   - استمرار العلاقات التجارية
   - تطور الهوية الوطنية المستقلة

3. مرحلة الاستقلال والتحالف (1961-1990):
   - اعتراف العراق باستقلال الكويت
   - دعم كويتي قوي للعراق في الحرب مع إيران
   - تدهور العلاقات في أواخر الثمانينات

4. مرحلة الأزمة (1990-1991):
   - الغزو العراقي للكويت
   - تدمير البنية التحتية الكويتية
   - تدخل دولي لتحرير الكويت

5. مرحلة القطيعة (1991-2003):
   - قطع كامل للعلاقات الدبلوماسية
   - عقوبات دولية على العراق
   - تعويضات الحرب

6. مرحلة التطبيع والتعاون (2003-الآن):
   - سقوط نظام صدام حسين
   - استئناف العلاقات الدبلوماسية
   - تطوير التعاون الاقتصادي والتجاري

التحديات الحالية:
- تسوية القضايا العالقة من فترة الغزو
- تطوير التعاون الاقتصادي
- تعزيز الأمن الإقليمي

الفرص المستقبلية:
- مشاريع البنية التحتية المشتركة
- التعاون في مجال الطاقة
- تطوير العلاقات التجارية والاستثمارية
"""
    
    filename = f"تحليل_العلاقات_الكويتية_العراقية_{datetime.datetime.now().strftime('%Y%m%d')}.txt"
    
    with open(filename, 'w', encoding='utf-8') as file:
        file.write(analysis_text)
    
    return filename

def main():
    """الدالة الرئيسية"""
    print("جاري إنشاء ملفات العلاقات الكويتية العراقية...")
    
    # إنشاء الملف الرئيسي
    main_file = create_kuwait_iraq_relations_csv()
    print(f"تم إنشاء الملف الرئيسي: {main_file}")
    
    # إنشاء ملف الملخص
    summary_file = create_summary_csv()
    print(f"تم إنشاء ملف الملخص: {summary_file}")
    
    # إنشاء التحليل المفصل
    analysis_file = create_detailed_analysis()
    print(f"تم إنشاء التحليل المفصل: {analysis_file}")
    
    print("\nتم إنشاء جميع الملفات بنجاح!")
    print("\nمحتويات الملفات:")
    print("1. الملف الرئيسي: تفاصيل تطور العلاقات عبر التاريخ")
    print("2. ملف الملخص: نظرة عامة على الفترات الرئيسية")
    print("3. التحليل المفصل: تحليل شامل للعلاقات والتحديات والفرص")
    
    print("\nيمكنك فتح ملفات CSV في برنامج Excel أو أي برنامج جداول بيانات")

if __name__ == "__main__":
    main()
