#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء شيت إكسل يوضح تطور العلاقات الكويتية العراقية عبر التاريخ
"""

from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
import datetime

def create_kuwait_iraq_relations_excel():
    """إنشاء شيت إكسل شامل للعلاقات الكويتية العراقية"""

    # البيانات التاريخية للعلاقات الكويتية العراقية
    historical_data = [
        {
            'الفترة الزمنية': '1600-1750',
            'السنة': '1600-1750',
            'الحدث/التطور': 'تأسيس الكويت وبداية العلاقات التجارية',
            'طبيعة العلاقة': 'تجارية ودية',
            'التفاصيل': 'تأسيس مدينة الكويت وبداية التجارة البحرية مع موانئ البصرة العراقية',
            'التأثير': 'إيجابي',
            'الأهمية': 'عالية'
        },
        {
            'الفترة الزمنية': '1750-1900',
            'السنة': '1750-1900',
            'الحدث/التطور': 'العلاقات التجارية في العهد العثماني',
            'طبيعة العلاقة': 'تجارية مستقرة',
            'التفاصيل': 'ازدهار التجارة البحرية بين الكويت والعراق تحت الحكم العثماني',
            'التأثير': 'إيجابي',
            'الأهمية': 'عالية'
        },
        {
            'الفترة الزمنية': '1900-1920',
            'السنة': '1918',
            'الحدث/التطور': 'انفصال الكويت عن العراق إدارياً',
            'طبيعة العلاقة': 'تغيير إداري',
            'التفاصيل': 'بريطانيا تفصل الكويت إدارياً عن العراق وتجعلها محمية بريطانية منفصلة',
            'التأثير': 'محايد',
            'الأهمية': 'عالية جداً'
        },
        {
            'الفترة الزمنية': '1920-1960',
            'السنة': '1920-1960',
            'الحدث/التطور': 'العلاقات في عهد الانتداب والاستقلال',
            'طبيعة العلاقة': 'دبلوماسية مستقرة',
            'التفاصيل': 'علاقات دبلوماسية وتجارية طبيعية بين البلدين',
            'التأثير': 'إيجابي',
            'الأهمية': 'متوسطة'
        },
        {
            'الفترة الزمنية': '1960-1980',
            'السنة': '1961',
            'الحدث/التطور': 'استقلال الكويت والاعتراف العراقي',
            'طبيعة العلاقة': 'دبلوماسية رسمية',
            'التفاصيل': 'استقلال الكويت واعتراف العراق بها كدولة مستقلة',
            'التأثير': 'إيجابي',
            'الأهمية': 'عالية جداً'
        },
        {
            'الفترة الزمنية': '1980-1988',
            'السنة': '1980-1988',
            'الحدث/التطور': 'الحرب العراقية الإيرانية والدعم الكويتي',
            'طبيعة العلاقة': 'تحالف وداعمة',
            'التفاصيل': 'الكويت تدعم العراق مالياً وسياسياً في حربه ضد إيران',
            'التأثير': 'إيجابي قوي',
            'الأهمية': 'عالية جداً'
        },
        {
            'الفترة الزمنية': '1988-1990',
            'السنة': '1988-1990',
            'الحدث/التطور': 'توتر العلاقات والخلافات الحدودية',
            'طبيعة العلاقة': 'متوترة',
            'التفاصيل': 'خلافات حول الديون والحدود وإنتاج النفط',
            'التأثير': 'سلبي',
            'الأهمية': 'عالية'
        },
        {
            'الفترة الزمنية': '1990',
            'السنة': '2 أغسطس 1990',
            'الحدث/التطور': 'الغزو العراقي للكويت',
            'طبيعة العلاقة': 'عدائية - احتلال',
            'التفاصيل': 'القوات العراقية تغزو وتحتل الكويت بالكامل',
            'التأثير': 'سلبي جداً',
            'الأهمية': 'عالية جداً'
        },
        {
            'الفترة الزمنية': '1991',
            'السنة': '26 فبراير 1991',
            'الحدث/التطور': 'تحرير الكويت',
            'طبيعة العلاقة': 'قطع العلاقات',
            'التفاصيل': 'تحرير الكويت من الاحتلال العراقي بعد حرب الخليج الثانية',
            'التأثير': 'إيجابي للكويت',
            'الأهمية': 'عالية جداً'
        },
        {
            'الفترة الزمنية': '1991-2003',
            'السنة': '1991-2003',
            'الحدث/التطور': 'قطع العلاقات والعقوبات الدولية',
            'طبيعة العلاقة': 'منقطعة',
            'التفاصيل': 'قطع كامل للعلاقات الدبلوماسية والاقتصادية',
            'التأثير': 'سلبي',
            'الأهمية': 'عالية'
        },
        {
            'الفترة الزمنية': '2003',
            'السنة': '9 أبريل 2003',
            'الحدث/التطور': 'سقوط نظام صدام حسين',
            'طبيعة العلاقة': 'بداية التطبيع',
            'التفاصيل': 'سقوط النظام العراقي السابق وبداية عهد جديد',
            'التأثير': 'إيجابي',
            'الأهمية': 'عالية جداً'
        },
        {
            'الفترة الزمنية': '2004-2010',
            'السنة': '2004-2010',
            'الحدث/التطور': 'استئناف العلاقات الدبلوماسية تدريجياً',
            'طبيعة العلاقة': 'تطبيع تدريجي',
            'التفاصيل': 'إعادة فتح السفارات وبداية التعاون الاقتصادي المحدود',
            'التأثير': 'إيجابي',
            'الأهمية': 'عالية'
        },
        {
            'الفترة الزمنية': '2010-2020',
            'السنة': '2010-2020',
            'الحدث/التطور': 'تطوير العلاقات الاقتصادية والتجارية',
            'طبيعة العلاقة': 'تعاون اقتصادي',
            'التفاصيل': 'زيادة التبادل التجاري والاستثمارات المتبادلة',
            'التأثير': 'إيجابي',
            'الأهمية': 'عالية'
        },
        {
            'الفترة الزمنية': '2020-2025',
            'السنة': '2020-2025',
            'الحدث/التطور': 'تعزيز الشراكة الاستراتيجية',
            'طبيعة العلاقة': 'شراكة استراتيجية',
            'التفاصيل': 'تطوير المشاريع المشتركة والتعاون في مختلف المجالات',
            'التأثير': 'إيجابي قوي',
            'الأهمية': 'عالية جداً'
        }
    ]

    # إنشاء ملف Excel
    wb = Workbook()
    ws = wb.active
    ws.title = "العلاقات الكويتية العراقية"

    # إعداد الخطوط والألوان
    header_font = Font(name='Arabic Typesetting', size=14, bold=True, color='FFFFFF')
    data_font = Font(name='Arabic Typesetting', size=12)
    header_fill = PatternFill(start_color='2E4A6B', end_color='2E4A6B', fill_type='solid')

    # ألوان مختلفة حسب طبيعة العلاقة
    positive_fill = PatternFill(start_color='D4F4DD', end_color='D4F4DD', fill_type='solid')
    negative_fill = PatternFill(start_color='F8D7DA', end_color='F8D7DA', fill_type='solid')
    neutral_fill = PatternFill(start_color='FFF3CD', end_color='FFF3CD', fill_type='solid')

    # إعداد الحدود
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # إضافة العنوان الرئيسي
    ws.merge_cells('A1:G1')
    ws['A1'] = 'تطور العلاقات الكويتية العراقية عبر التاريخ'
    ws['A1'].font = Font(name='Arabic Typesetting', size=18, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    ws['A1'].fill = PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid')
    ws['A1'].font = Font(name='Arabic Typesetting', size=18, bold=True, color='FFFFFF')

    # إضافة تاريخ الإنشاء
    ws.merge_cells('A2:G2')
    ws['A2'] = f'تاريخ الإنشاء: {datetime.datetime.now().strftime("%Y-%m-%d")}'
    ws['A2'].font = Font(name='Arabic Typesetting', size=10, italic=True)
    ws['A2'].alignment = Alignment(horizontal='center')

    # إضافة رؤوس الأعمدة
    headers = ['الفترة الزمنية', 'السنة', 'الحدث/التطور', 'طبيعة العلاقة', 'التفاصيل', 'التأثير', 'الأهمية']
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=4, column=col_num)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        cell.border = thin_border

    # إضافة البيانات
    for row_num, data_row in enumerate(historical_data, 5):
        row_data = [data_row['الفترة الزمنية'], data_row['السنة'], data_row['الحدث/التطور'],
                   data_row['طبيعة العلاقة'], data_row['التفاصيل'], data_row['التأثير'], data_row['الأهمية']]

        for col_num, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.value = value
            cell.font = data_font
            cell.alignment = Alignment(horizontal='right', vertical='center', wrap_text=True)
            cell.border = thin_border

            # تلوين الصفوف حسب طبيعة العلاقة
            if 'إيجابي' in str(data_row['التأثير']):
                cell.fill = positive_fill
            elif 'سلبي' in str(data_row['التأثير']):
                cell.fill = negative_fill
            else:
                cell.fill = neutral_fill

    # تعديل عرض الأعمدة
    column_widths = [15, 12, 25, 20, 40, 15, 12]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[chr(64 + i)].width = width

    # تعديل ارتفاع الصفوف
    for row in range(4, ws.max_row + 1):
        ws.row_dimensions[row].height = 30

    ws.row_dimensions[1].height = 40
    ws.row_dimensions[4].height = 35

    return wb

def create_summary_sheet(wb):
    """إنشاء ورقة ملخص للعلاقات"""
    ws_summary = wb.create_sheet("ملخص العلاقات")

    # إعداد الخطوط
    title_font = Font(name='Arabic Typesetting', size=16, bold=True)
    header_font = Font(name='Arabic Typesetting', size=14, bold=True)
    data_font = Font(name='Arabic Typesetting', size=12)

    # العنوان
    ws_summary['A1'] = 'ملخص تطور العلاقات الكويتية العراقية'
    ws_summary['A1'].font = title_font
    ws_summary['A1'].alignment = Alignment(horizontal='center')
    ws_summary.merge_cells('A1:D1')

    # الفترات الرئيسية
    periods_data = [
        ['الفترة', 'الوصف', 'طبيعة العلاقة', 'التقييم'],
        ['1600-1918', 'العهد العثماني', 'تجارية مستقرة', 'إيجابية'],
        ['1918-1961', 'فترة الانتداب', 'دبلوماسية عادية', 'مستقرة'],
        ['1961-1990', 'ما بعد الاستقلال', 'تحالف ثم توتر', 'متقلبة'],
        ['1990-1991', 'الغزو والتحرير', 'عدائية', 'سلبية جداً'],
        ['1991-2003', 'قطع العلاقات', 'منقطعة', 'سلبية'],
        ['2003-الآن', 'التطبيع والتعاون', 'شراكة متنامية', 'إيجابية متزايدة']
    ]

    # إضافة البيانات
    for row_num, row_data in enumerate(periods_data, 3):
        for col_num, value in enumerate(row_data, 1):
            cell = ws_summary.cell(row=row_num, column=col_num)
            cell.value = value
            if row_num == 3:  # رؤوس الأعمدة
                cell.font = header_font
                cell.fill = PatternFill(start_color='2E4A6B', end_color='2E4A6B', fill_type='solid')
                cell.font = Font(name='Arabic Typesetting', size=14, bold=True, color='FFFFFF')
            else:
                cell.font = data_font
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

    # تعديل عرض الأعمدة
    for i, width in enumerate([15, 25, 20, 20], 1):
        ws_summary.column_dimensions[chr(64 + i)].width = width

    return wb

def main():
    """الدالة الرئيسية"""
    print("جاري إنشاء شيت إكسل للعلاقات الكويتية العراقية...")

    # إنشاء الملف الرئيسي
    wb = create_kuwait_iraq_relations_excel()

    # إضافة ورقة الملخص
    wb = create_summary_sheet(wb)

    # حفظ الملف
    filename = f"العلاقات_الكويتية_العراقية_{datetime.datetime.now().strftime('%Y%m%d')}.xlsx"
    wb.save(filename)

    print(f"تم إنشاء الملف بنجاح: {filename}")
    print("\nمحتويات الملف:")
    print("- ورقة العمل الرئيسية: تفاصيل تطور العلاقات عبر التاريخ")
    print("- ورقة الملخص: نظرة عامة على الفترات الرئيسية")
    print("\nالملف يحتوي على:")
    print("- تنسيق عربي مع خطوط مناسبة")
    print("- ألوان مختلفة حسب طبيعة العلاقة")
    print("- تفاصيل شاملة للأحداث التاريخية المهمة")

if __name__ == "__main__":
    main()
