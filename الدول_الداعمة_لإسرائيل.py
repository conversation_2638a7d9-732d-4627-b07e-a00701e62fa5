#!/usr/bin/env python3
# -*- coding: utf-8 -*-

try:
    import pandas as pd
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.utils.dataframe import dataframe_to_rows
except ImportError:
    print("تثبيت المكتبات المطلوبة...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "pandas", "openpyxl"])
    import pandas as pd
    from openpyxl import Workbook
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    from openpyxl.utils.dataframe import dataframe_to_rows

import os

def create_israel_supporters_excel():
    """إنشاء شيت إكسل للدول الداعمة لإسرائيل مع تمييز الدول العربية"""

    # البيانات - الدول الداعمة لإسرائيل
    data = [
        {"الدولة": "الولايات المتحدة الأمريكية", "نوع الدعم": "عسكري ومالي وسياسي", "مبلغ المساعدات (مليار دولار سنوياً)": "3.8", "عربية": "لا"},
        {"الدولة": "ألمانيا", "نوع الدعم": "عسكري ومالي وسياسي", "مبلغ المساعدات (مليار دولار سنوياً)": "0.5", "عربية": "لا"},
        {"الدولة": "المملكة المتحدة", "نوع الدعم": "سياسي وتجاري", "مبلغ المساعدات (مليار دولار سنوياً)": "0.2", "عربية": "لا"},
        {"الدولة": "فرنسا", "نوع الدعم": "سياسي وتجاري", "مبلغ المساعدات (مليار دولار سنوياً)": "0.1", "عربية": "لا"},
        {"الدولة": "كندا", "نوع الدعم": "سياسي ومالي", "مبلغ المساعدات (مليار دولار سنوياً)": "0.05", "عربية": "لا"},
        {"الدولة": "أستراليا", "نوع الدعم": "سياسي وتجاري", "مبلغ المساعدات (مليار دولار سنوياً)": "0.03", "عربية": "لا"},
        {"الدولة": "هولندا", "نوع الدعم": "سياسي ومالي", "مبلغ المساعدات (مليار دولار سنوياً)": "0.02", "عربية": "لا"},
        {"الدولة": "الإمارات العربية المتحدة", "نوع الدعم": "تطبيع واتفاقيات تجارية", "مبلغ المساعدات (مليار دولار سنوياً)": "غير محدد", "عربية": "نعم"},
        {"الدولة": "البحرين", "نوع الدعم": "تطبيع وتعاون أمني", "مبلغ المساعدات (مليار دولار سنوياً)": "غير محدد", "عربية": "نعم"},
        {"الدولة": "المغرب", "نوع الدعم": "تطبيع وتعاون", "مبلغ المساعدات (مليار دولار سنوياً)": "غير محدد", "عربية": "نعم"},
        {"الدولة": "السودان", "نوع الدعم": "تطبيع (سابق)", "مبلغ المساعدات (مليار دولار سنوياً)": "غير محدد", "عربية": "نعم"},
        {"الدولة": "مصر", "نوع الدعم": "اتفاقية سلام وتعاون أمني", "مبلغ المساعدات (مليار دولار سنوياً)": "غير محدد", "عربية": "نعم"},
        {"الدولة": "الأردن", "نوع الدعم": "اتفاقية سلام وتعاون", "مبلغ المساعدات (مليار دولار سنوياً)": "غير محدد", "عربية": "نعم"},
        {"الدولة": "إيطاليا", "نوع الدعم": "سياسي وتجاري", "مبلغ المساعدات (مليار دولار سنوياً)": "0.01", "عربية": "لا"},
        {"الدولة": "إسبانيا", "نوع الدعم": "سياسي محدود", "مبلغ المساعدات (مليار دولار سنوياً)": "0.005", "عربية": "لا"},
        {"الدولة": "اليابان", "نوع الدعم": "تجاري وتقني", "مبلغ المساعدات (مليار دولار سنوياً)": "0.02", "عربية": "لا"},
        {"الدولة": "كوريا الجنوبية", "نوع الدعم": "تجاري وتقني", "مبلغ المساعدات (مليار دولار سنوياً)": "0.01", "عربية": "لا"},
        {"الدولة": "الهند", "نوع الدعم": "تجاري وتقني", "مبلغ المساعدات (مليار دولار سنوياً)": "غير محدد", "عربية": "لا"},
        {"الدولة": "البرازيل", "نوع الدعم": "تجاري محدود", "مبلغ المساعدات (مليار دولار سنوياً)": "غير محدد", "عربية": "لا"},
        {"الدولة": "المكسيك", "نوع الدعم": "سياسي محدود", "مبلغ المساعدات (مليار دولار سنوياً)": "غير محدد", "عربية": "لا"}
    ]

    # إنشاء DataFrame
    df = pd.DataFrame(data)

    # إنشاء workbook جديد
    wb = Workbook()
    ws = wb.active
    ws.title = "الدول الداعمة لإسرائيل"

    # إضافة البيانات إلى الورقة
    for r in dataframe_to_rows(df, index=False, header=True):
        ws.append(r)

    # تنسيق الخطوط العربية
    arabic_font = Font(name='Arabic Typesetting', size=12, bold=False)
    header_font = Font(name='Arabic Typesetting', size=14, bold=True, color='FFFFFF')

    # تنسيق الحدود
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )

    # تنسيق الرأس
    header_fill = PatternFill(start_color='2F4F4F', end_color='2F4F4F', fill_type='solid')

    # تطبيق التنسيق على الرأس
    for col in range(1, len(df.columns) + 1):
        cell = ws.cell(row=1, column=col)
        cell.font = header_font
        cell.fill = header_fill
        cell.border = thin_border
        cell.alignment = Alignment(horizontal='center', vertical='center', text_rotation=0, wrap_text=True)

    # تنسيق البيانات
    for row in range(2, len(df) + 2):
        for col in range(1, len(df.columns) + 1):
            cell = ws.cell(row=row, column=col)
            cell.font = arabic_font
            cell.border = thin_border
            cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

            # تمييز الدول العربية بخط أحمر
            if col == 1:  # عمود الدولة
                country_name = cell.value
                # البحث عن الدولة في البيانات للتحقق من كونها عربية
                for data_row in data:
                    if data_row["الدولة"] == country_name and data_row["عربية"] == "نعم":
                        cell.font = Font(name='Arabic Typesetting', size=12, bold=True, color='FF0000')
                        break

    # تعديل عرض الأعمدة
    column_widths = [30, 25, 20, 10]  # عرض كل عمود
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[ws.cell(row=1, column=i).column_letter].width = width

    # تعديل ارتفاع الصفوف
    for row in range(1, len(df) + 2):
        ws.row_dimensions[row].height = 25

    # إضافة ملاحظة في الأسفل
    note_row = len(df) + 3
    ws.cell(row=note_row, column=1, value="ملاحظة: الدول العربية مميزة بالخط الأحمر")
    note_cell = ws.cell(row=note_row, column=1)
    note_cell.font = Font(name='Arabic Typesetting', size=11, italic=True, color='FF0000')

    # دمج الخلايا للملاحظة
    ws.merge_cells(f'A{note_row}:D{note_row}')

    # حفظ الملف
    filename = "الدول_الداعمة_لإسرائيل.xlsx"
    wb.save(filename)

    print(f"تم إنشاء الملف بنجاح: {filename}")
    print("الدول العربية مميزة بالخط الأحمر في العمود الأول")

    return filename

if __name__ == "__main__":
    create_israel_supporters_excel()
